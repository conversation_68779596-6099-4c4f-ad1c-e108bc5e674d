"""
Prompt templates for AI question generation
"""

EVALUATION_QUESTIONS_PROMPT = """
You are an expert educator creating evaluation questions for academic projects. You MUST respond with ONLY valid JSON format.

**Project Information:**
- Title: {project_title}
- Course Track: {course_track}
- Description: {project_description}

**Project Requirements (from PDF):**
{project_content}

**Task:** Generate {num_questions} evaluation questions for assessing student submissions.

**Instructions:**
1. Analyze the project type (coding, AI/ML, web development, research, design, etc.)
2. Identify the most important aspects that need evaluation based on the project requirements
3. Create {num_questions} distinct evaluation questions that assess different critical aspects
4. Each question should be clear, specific, and answerable with Yes/No by an evaluator
5. Questions should be directed at the evaluator (e.g., "Does the student's work...", "Is the submission...")
6. Include any evaluation guidance directly in the question text if needed
7. It's acceptable for one question to cover multiple related aspects

**CRITICAL FORMATTING REQUIREMENTS:**
- Response must be VALID JSON only
- No additional text, explanations, or formatting outside the JSON
- No markdown, no code blocks, no extra characters
- Use double quotes for all strings
- Ensure proper JSON syntax with commas and brackets

**Required JSON Structure:**
{{
  "questions": [
    {{
      "text": "Question text here",
      "response_type": "boolean"
    }}
  ]
}}

**JSON Response Guidelines:**
- "text": Should be a clear, specific evaluation question for the evaluator
- "response_type": Always set to "boolean"
- Questions can be compound (covering multiple aspects in one question)
- Include evaluation guidance in the question text if helpful

**Example JSON Response:**
{{
  "questions": [
    {{
      "text": "Does the student's submission include all required deliverables and demonstrate proper functionality as specified in the project requirements?",
      "response_type": "boolean"
    }},
    {{
      "text": "Is the student's work well-documented with clear explanations of the methodology and implementation approach?",
      "response_type": "boolean"
    }}
  ]
}}

RESPOND WITH VALID JSON ONLY - NO OTHER TEXT OR FORMATTING:
"""

EVALUATION_QUESTIONS_REFINEMENT_PROMPT = """
You are an expert educator refining evaluation questions based on user feedback. You MUST respond with ONLY valid JSON format.

**Project Information:**
- Title: {project_title}
- Course Track: {course_track}
- Description: {project_description}

**Project Requirements (from PDF):**
{project_content}

**PREVIOUS QUESTIONS I GENERATED:**
{previous_questions}

**USER FEEDBACK/INSTRUCTIONS FOR REFINEMENT:**
{user_instructions}

**Task:** Generate {num_questions} REFINED evaluation questions that improve upon the previous questions based on the user's specific feedback.

**Instructions:**
1. **Review the previous questions** - Understand what I generated before
2. **Analyze the user feedback** - Identify what they want changed, improved, or emphasized
3. **Refine strategically** - Improve the previous questions rather than creating completely new ones
4. **Incorporate user directions** - Focus on the specific aspects, areas, or improvements they requested
5. **Maintain evaluator perspective** - Questions should still be for evaluators assessing student work
6. **Keep the good parts** - If some previous questions were good, keep and improve them

**Refinement Strategies:**
- If user wants "more technical focus" → Make questions more specific about technical implementation
- If user wants "user experience emphasis" → Add/modify questions about usability, interface, user testing
- If user wants "better documentation focus" → Enhance questions about code comments, documentation quality
- If user wants "more specific criteria" → Make questions more detailed and specific to the project
- If user wants "broader coverage" → Ensure questions cover different aspects of the project

**CRITICAL FORMATTING REQUIREMENTS:**
- Response must be VALID JSON only
- No additional text, explanations, or formatting outside the JSON
- Use double quotes for all strings
- Ensure proper JSON syntax

**Required JSON Structure:**
{{
  "questions": [
    {{
      "text": "Refined question text based on user feedback",
      "response_type": "boolean"
    }}
  ]
}}

**Important:** This is a refinement process - improve and adjust the previous questions based on the user's specific feedback, don't ignore what was previously generated.

RESPOND WITH VALID JSON ONLY - NO OTHER TEXT OR FORMATTING:
"""