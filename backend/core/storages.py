# Standard library imports
import os
import time
import uuid
from pathlib import Path

# Third-party imports
import boto3
from botocore.exceptions import ClientError

# Django imports
from django.conf import settings

def generate_filename(original_filename, prefix='test_', env=None):
    """
    Generate a filename with test-specific prefix in development environment.
    
    Args:
        original_filename (str): Original filename
        prefix (str, optional): Prefix for test files. Defaults to 'test_'.
        env (str, optional): Environment. Uses settings.DEV_ENV if not provided.
    
    Returns:
        str: Modified filename for testing
    """
    # Use environment from settings if not provided
    env = env or os.getenv('DEV_ENV', None) or 'development'
    ext = original_filename.split('.')[-1]
    unique_id = f'{uuid.uuid4().hex}_{int(time.time())}'

    # Only modify filename in development environment
    if env == 'development':
        return f'{prefix}-{original_filename}-{unique_id}.{ext}'
    return f'{original_filename}-{unique_id}.{ext}'

def cleanup_test_files(directory=None):
    """
    Remove all test files from the specified directory.
    
    Supports both local filesystem and cloud storage (Digital Ocean Spaces/S3).
    
    Args:
        directory (str, optional): Directory to clean. 
                                   Uses settings.MEDIA_ROOT if not provided.
    """
    # Determine the directory to clean
    if directory is None:
        directory = settings.MEDIA_ROOT

    # Check if using cloud storage
    if settings.STORAGES.get('default', {}).get('BACKEND', '').endswith('S3Boto3Storage'):
        try:
            # Get storage configuration
            storage_options = settings.STORAGES['default']['OPTIONS']
            
            # Initialize S3 client
            s3 = boto3.client(
                's3',
                endpoint_url=storage_options['endpoint_url'],
                aws_access_key_id=storage_options['access_key'],
                aws_secret_access_key=storage_options['secret_key']
            )
            
            # Bucket name from storage options
            bucket_name = storage_options['bucket_name']
            
            # Determine the location from storage options
            location = storage_options.get('location', '').strip('/')
            
            # Construct the prefix based on directory and location
            directory_part = str(directory).strip('/') if directory else ''
            prefix_parts = []
            if location:
                prefix_parts.append(location)
            if directory_part:
                prefix_parts.append(directory_part)
            prefix_parts.append('test_')
            prefix = '/'.join(prefix_parts)
            
            print(f"Cleaning up files in prefix: {prefix} in bucket: {bucket_name}")
            
            # List objects in the bucket with the constructed prefix
            paginator = s3.get_paginator('list_objects_v2')
            for result in paginator.paginate(
                Bucket=bucket_name, 
                Prefix=prefix
            ):
                for obj in result.get('Contents', []):
                    try:
                        s3.delete_object(Bucket=bucket_name, Key=obj['Key'])
                        print(f"Deleted cloud file: {obj['Key']}")
                    except ClientError as e:
                        print(f"Could not delete cloud file {obj['Key']}: {e}")
        
        except Exception as e:
            print(f"Error cleaning up cloud storage: {e}")
    else:
        # Handle local filesystem
        try:
            dir_path = Path(directory).resolve()
            if not dir_path.exists():
                print(f"Directory {directory} does not exist.")
                return
            
            # Iterate through all files and directories in dir_path
            for file_path in dir_path.glob('**/*'):
                if file_path.is_file():
                    try:
                        rel_path = file_path.relative_to(dir_path)
                        rel_path_posix = rel_path.as_posix()
                        if rel_path_posix.startswith('test_'):
                            file_path.unlink()
                            print(f"Deleted local file: {file_path}")
                    except ValueError:
                        # Ignore files not relative to dir_path (unlikely due to glob)
                        pass
                    except Exception as e:
                        print(f"Could not delete local file {file_path}: {e}")
        except Exception as e:
            print(f"Error cleaning up local files: {e}")
