# Django imports
from django.core.management.base import BaseCommand

# Local imports
from core.storages import cleanup_test_files


class Command(BaseCommand):
    help = 'Clean up test files from media directory'

    def handle(self, *args, **kwargs):
        # Attempt to import the cleanup function
        try:
            directories = ['projects/pdfs', 'projects/submissions']
            for directory in directories:
                cleanup_test_files(directory)
            self.stdout.write(
                self.style.SUCCESS(f'Successfully cleaned up test files in {directory}')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error during cleanup: {e}')
            )
