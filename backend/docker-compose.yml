services:
  backend:
    build: .
    volumes:
      - .:/app
      - static_volume:/app/staticfiles
      - media_volume:/app/media
    env_file:
      - ./.env
    environment:
      - RUN_MIGRATIONS_ONLY=0
      - POSTGRES_HOST=db
      - POSTGRES_PORT=5432
      - USE_SPACES=${USE_SPACES}
      - SPACES_ACCESS_KEY=${SPACES_ACCESS_KEY}
      - SPACES_SECRET_KEY=${SPACES_SECRET_KEY}
      - SPACES_BUCKET_NAME=${SPACES_BUCKET_NAME}
      - SPACES_REGION=${SPACES_REGION}
    depends_on:
      db:
        condition: service_healthy
      migrations:
        condition: service_completed_successfully
    expose:
      - "8000"
    restart: always
    deploy:
      replicas: 2
      update_config:
        parallelism: 1
        delay: 10s
        order: stop-first

  migrations:
    build: .
    volumes:
      - .:/app
    env_file:
      - ./.env
    environment:
      - POSTGRES_HOST=db
      - POSTGRES_PORT=5432
      - RUN_MIGRATIONS_ONLY=1
    depends_on:
      db:
        condition: service_healthy
    restart: "no"

  nginx:
    build: ./nginx
    volumes:
      - static_volume:/app/staticfiles
      - media_volume:/app/media
      - ./nginx/sites-available:/etc/nginx/sites-available
      - ./nginx/ssl/cloudflare:/etc/nginx/ssl/cloudflare
    environment:
      - DEBUG=${DEBUG}
      - DEV_ENV=${DEV_ENV}
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - backend
    restart: always

  db:
    image: postgres:13
    volumes:
      - postgres_data:/var/lib/postgresql/data
    env_file:
      - ./.env
    expose:
      - "5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5
    restart: always

volumes:
  postgres_data:
  static_volume:
  media_volume:
  cloudflare:
