from django.urls import path
from . import views

app_name = 'projects'

urlpatterns = [
    path('cancel-upload/', views.CancelUploadView.as_view(), name='cancel-upload'),
    path('my-submissions/', views.UserProjectSubmissionsView.as_view(), name='my-submissions'),
    path('submit/', views.ProjectSubmissionView.as_view(), name='submit'),
    path('pool/', views.EvaluationPoolView.as_view(), name='evaluation-pool'),
    path('evaluation/<int:pk>/', views.EvaluationDetailView.as_view(), name='evaluation-detail'),
    path('evaluate/<int:pk>/', views.EvaluationView.as_view(), name='evaluate'),
    path('submission/<int:pk>/status/', views.SubmissionStatusView.as_view(), name='submission-status'),

    # Course-related URLs - more specific routes first
    path('courses/register/<str:course_code>/', views.CourseRegisterView.as_view(), name='course-register'),
    path('courses/registrations/<int:request_id>/approve/', views.CourseRegistrationApproveView.as_view(), name='course-registration-approve'),
    path('courses/registrations/<int:request_id>/reject/', views.CourseRegistrationRejectView.as_view(), name='course-registration-reject'),
    path('courses/registrations/pending/', views.AllRegistrationRequestsView.as_view(), name='all-registration-requests'),
    path('courses/', views.CourseListCreateView.as_view(), name='course-list-create'),

    # Course URLs with parameters - these come after specific routes
    path('courses/<str:course_code>/projects/', views.ProjectListCreateView.as_view(), name='course-project-list-create'),
    path('courses/<str:course_code>/projects/<int:project_id>/', 
         views.ProjectDetailUpdateView.as_view(), 
         name='project-detail-update'),
    path('courses/<str:course_code>/projects/<int:project_id>/questions/', views.QuestionCreateView.as_view(), name='question-create'),
    # Add this line to urlpatterns
    path('generate-questions-from-pdf/', views.AIQuestionGeneratorView.as_view(), name='generate-questions-from-pdf'),
    path('courses/<str:course_code>/next/', views.NextProjectView.as_view(), name='course-next-project'),
    path('courses/<str:course_code>/registrations/', views.CourseRegistrationListView.as_view(), name='course-registration-list'),
    path('courses/<str:course_code>/', views.CourseDetailView.as_view(), name='course-detail'),
    path('refine-questions-from-pdf/', views.AIQuestionRefinementView.as_view(), name='refine-questions-from-pdf'),
]
