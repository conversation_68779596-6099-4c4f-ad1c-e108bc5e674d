# Standard library imports
import os
import random
import string

# Django imports
from django.conf import settings
from django.core.exceptions import ValidationError
from django.db import models
from django.db.models import Sum

# Local imports
from core.storages import generate_filename
from users.models import User, Track


class Course(models.Model):
    code = models.CharField(max_length=20, primary_key=True, unique=True, blank=True)
    name = models.CharField(max_length=150, null=False)
    description = models.TextField(max_length=100, blank=False, null=False)
    educator = models.ForeignKey(User, 
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='courses'
    )
    track = models.ForeignKey(Track, 
        on_delete=models.CASCADE,
        related_name='courses',
        null=True,  # Keep as nullable in the database schema
        blank=False  # But make it required in forms
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        if not self.code:
            self.code = self.generate_unique_code()
        super().save(*args, **kwargs)

    @staticmethod
    def generate_unique_code(length=8):
        while True:
            code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=length))
            if not Course.objects.filter(code=code).exists():
                return code

    def __str__(self):
        return f"{self.name} ({self.code}) - {self.educator}"

class CourseRegistrationRequest(models.Model):
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
    ]
    student = models.ForeignKey(User, on_delete=models.CASCADE, related_name='course_registration_requests')
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='registration_requests')
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='pending')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ('student', 'course')

    def __str__(self):
        return f"{self.student.username} - {self.course.name} ({self.status})"

def validate_pdf_file(value):
    validate_file_extension(value, ['pdf'], ValidationError)

def validate_zip_file(value):
    validate_file_extension(value, ['zip'], ValueError)

def get_project_pdf_path(instance, filename):
    ext = filename.split('.')[-1]
    filename = generate_filename(filename)
    return os.path.join('projects/pdfs', filename)

def get_submission_zip_path(instance, filename):
    ext = filename.split('.')[-1]
    if ext.lower() != 'zip':
        raise ValueError('Only ZIP files are allowed')
    filename = generate_filename(filename)
    return os.path.join('projects/submissions', filename)

def validate_file_size(value):
    filesize = value.size
    if filesize > 50 * 1024 * 1024:  # 50MB
        raise ValidationError("Maximum file size that can be uploaded is 50MB")

def validate_file_extension(value, allowed_extensions, error_type=ValidationError):
    ext = os.path.splitext(value.name)[1].lower()
    if ext.startswith('.'):
        ext = ext[1:]
    if ext not in allowed_extensions:
        raise error_type(f"Invalid file extension: .{ext}")

def validate_filename_safe(value):
    # Block directory traversal or absolute path
    if '..' in value.name or value.name.startswith('/') or value.name.startswith('\\'):
        raise ValidationError("Suspicious or unsafe filename detected.")

class Project(models.Model):
    title = models.CharField(max_length=200)
    description = models.TextField()
    pdf_file = models.FileField(
        upload_to=get_project_pdf_path,
        validators=[validate_file_size, validate_pdf_file, validate_filename_safe],
        blank=True,
        null=True
    )
    points_required = models.IntegerField(default=1)
    passing_score = models.FloatField(default=70.0, help_text="Minimum score (%) required to pass this project")
    required_evaluators = models.IntegerField(default=1, help_text="Number of evaluators required to complete this project")
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name="projects", null=False)
    prerequisite = models.ForeignKey('self', null=True, blank=True, on_delete=models.SET_NULL, related_name='dependent_projects')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def clean(self):
        try:
            if not self.course:
                raise ValidationError("Course is required for a project.")
        except Project.course.RelatedObjectDoesNotExist:
            raise ValidationError("Course is required for a project.")
        # Prevent self-reference
        if self.prerequisite and self.prerequisite == self:
            raise ValidationError("A project cannot be its own prerequisite.")
        # Prevent circular prerequisites
        visited = set()
        current = self.prerequisite
        while current:
            if current == self:
                raise ValidationError("Circular prerequisite detected.")
            if current.pk in visited:
                break  # Prevent infinite loop in case of broken chain
            visited.add(current.pk)
            current = current.prerequisite
            
        # Validate passing_score is between 0 and 100
        if self.passing_score < 0 or self.passing_score > 100:
            raise ValidationError("Passing score must be between 0 and 100.")
            
        super().clean()
    def save(self, *args, **kwargs):
        self.full_clean()
        super().save(*args, **kwargs)

    def __str__(self):
        prereq = f" | Prereq: {self.prerequisite.title}" if self.prerequisite else ""
        return f"{self.title}{prereq}"

class Question(models.Model):
    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name='questions')
    text = models.TextField()
    response = models.BooleanField(default=False)
    weight = models.FloatField(default=1, help_text="Question weight (relative importance)")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def clean(self):
        if not self.project:
            raise ValidationError("Project is required for a question")
        
        if self.weight < 0:
            raise ValidationError("Weight must be a non-negative number")
            
        # Calculate total weight of existing questions in the project
        total_weight = Question.objects.filter(
            project=self.project).exclude(pk=self.pk).aggregate(Sum('weight'))['weight__sum'] or 0
            
        # Allow for small floating-point precision errors (tolerance of 0.01)
        if total_weight + self.weight > 100.01:
            raise ValidationError(
                f"Total weight for all questions in project must be 100%. "
                f"Current total with this question would be {total_weight + self.weight:.6f}%"
            )
        super().clean()

    def save(self, *args, **kwargs):
        self.full_clean()
        super().save(*args, **kwargs)

    def __str__(self):
        return self.text

    def get_weighted_score(self):
        """Calculate this question's contribution to the total score"""
        return self.weight

class ProjectSubmission(models.Model):
    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name='submissions')
    submitted_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='submitted_projects')
    github_repo = models.URLField(blank=True, null=True)
    zip_file = models.FileField(
        upload_to=get_submission_zip_path,
        blank=True,
        null=True,
        validators=[validate_file_size, validate_zip_file, validate_filename_safe]
    )
    submission_type = models.CharField(max_length=10, choices=[
        ('github', 'GitHub Repository'),
        ('zip', 'ZIP File')
    ], default='github')
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('in_evaluation', 'In Evaluation'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ]
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    final_score = models.FloatField(null=True, blank=True, help_text="Final score after all evaluations")
    assigned_evaluator = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='assigned_submissions')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def clean(self):
        super().clean()
        # Check if prerequisite project is completed
        if self.project.prerequisite:
            completed_submission = ProjectSubmission.objects.filter(
                project=self.project.prerequisite,
                submitted_by=self.submitted_by,
                status='completed'
            ).first()
            if not completed_submission:
                raise ValidationError("Prerequisite project must be completed first")

        # Check if user has enough points for this project (only on creation, not updates)
        if self.pk is None and self.submitted_by.points < self.project.points_required:
            raise ValidationError(f"User needs at least {self.project.points_required} points to submit this project")

        # Check that the submitter is a student (not an educator)
        if self.submitted_by.user_type == 'E':
            raise ValidationError("Educators cannot submit projects, only students can")

    def save(self, *args, **kwargs):
        self.full_clean()
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.project.title} by {self.submitted_by.username}"

class Evaluation(models.Model):
    submission = models.ForeignKey(ProjectSubmission, on_delete=models.CASCADE, related_name='evaluations')
    evaluator = models.ForeignKey(User, on_delete=models.CASCADE, related_name='evaluations')
    comments = models.TextField()
    is_approved = models.BooleanField(default=False)
    score = models.FloatField(null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def clean(self):
        # Ensure evaluator is a student (educators cannot evaluate)
        if self.evaluator.user_type == 'E':
            raise ValidationError("Educators cannot evaluate projects, only students can.")
            
        # Ensure evaluator is enrolled in the course
        if not self.evaluator.enrolled_courses.filter(code=self.submission.project.course.code).exists():
            raise ValidationError("Evaluators can only evaluate submissions from users in the same course.")
            
        # Prevent duplicate evaluations by the same evaluator
        if Evaluation.objects.filter(submission=self.submission, evaluator=self.evaluator).exclude(pk=self.pk).exists():
            raise Exception("Evaluator cannot evaluate the same submission twice.")
            
        # Ensure submission is not completed
        if self.submission.status == 'completed':
            raise Exception("Cannot evaluate a submission after it is completed.")
            
        # Score bounds
        if self.score is not None and (self.score < 0 or self.score > 100):
            raise ValidationError("Score must be between 0 and 100.")
            
        # Feedback length
        if not self.comments or len(self.comments.strip()) == 0:
            raise ValidationError("Feedback/comments cannot be empty.")
        if len(self.comments) > 5000:
            raise ValidationError("Feedback/comments is too long (max 5000 characters).")
            
        super().clean()

    def save(self, *args, **kwargs):
        self.full_clean()
        super().save(*args, **kwargs)

    def __str__(self):
        return f"Evaluation for {self.submission}"
