{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@dnd-kit/core": "6.3.1", "@dnd-kit/sortable": "10.0.0", "@dnd-kit/utilities": "3.2.2", "@emotion/react": "11.13.5", "@emotion/styled": "11.13.5", "@mui/icons-material": "6.1.9", "@mui/material": "6.1.9", "axios": "1.7.8", "motion": "11.13.1", "react": "18.3.1", "react-dom": "18.3.1", "react-dropzone": "14.3.5", "react-markdown": "9.0.1", "react-router-dom": "7.0.2"}, "devDependencies": {"@eslint/js": "9.15.0", "@types/node": "22.10.1", "@types/react": "18.3.12", "@types/react-dom": "18.3.1", "@vitejs/plugin-react": "4.3.4", "eslint": "9.15.0", "eslint-plugin-react-hooks": "5.0.0", "eslint-plugin-react-refresh": "0.4.14", "globals": "15.12.0", "typescript": "5.6.2", "typescript-eslint": "8.15.0", "vite": "6.0.11"}}