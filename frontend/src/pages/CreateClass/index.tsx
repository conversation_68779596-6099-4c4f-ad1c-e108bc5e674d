import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  Stepper,
  Step,
  <PERSON><PERSON>abel,
  Button,
  Typography,
  TextField,
  Paper,
  useTheme,
  CircularProgress,
} from "@mui/material";
import { useNavigate } from "react-router-dom";
import PDFManagement from "@/components/PDFManagement";
import EvaluationSheetManagement, {
  EvaluationSheet,
} from "@/components/EvaluationSheetManagement";
import { courseService, CreateClassRequest } from "@/services/courseService";

interface PDFContent {
  id: string;
  file: File;
  title: string;
  description: string;
  prerequisiteId?: string | null;
}

interface ClassFormData {
  name: string;
  description: string;
  pdfs: PDFContent[];
  evaluationSheet: EvaluationSheet;
}

const steps = [
  "Basic Information",
  "Content & PDFs",
  "Evaluation Sheets",
  "Review",
];

const ReviewError: React.FC<{ error?: string }> = ({ error }) => {
  if (!error) return null;

  return (
    <Paper
      sx={{
        p: 2,
        mb: 3,
        backgroundColor: "error.dark",
        color: "error.contrastText",
      }}
      data-error="submit"
    >
      <Typography>{error}</Typography>
    </Paper>
  );
};

const CreateClass: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const [activeStep, setActiveStep] = useState(0);
  const [formData, setFormData] = useState<ClassFormData>({
    name: "",
    description: "",
    pdfs: [],
    evaluationSheet: {
      pdfEvaluations: [],
      totalWeight: 0,
    },
  });
  const [errors, setErrors] = useState<{
    name?: string;
    description?: string;
    pdfs?: string;
    evaluationSheet?: string;
    submit?: string;
  }>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Keep evaluationSheet.pdfEvaluations in sync with pdfs array
  useEffect(() => {
    const currentPdfIds = new Set(
      formData.evaluationSheet.pdfEvaluations.map((pe) => pe.pdfId)
    );
    const updatedPdfEvaluations = [...formData.evaluationSheet.pdfEvaluations];

    // Add evaluation sections for new PDFs
    formData.pdfs.forEach((pdf) => {
      if (!currentPdfIds.has(pdf.id)) {
        updatedPdfEvaluations.push({
          pdfId: pdf.id,
          questions: [],
          totalWeight: 0,
        });
      }
    });

    // Remove evaluation sections for deleted PDFs
    const validPdfIds = new Set(formData.pdfs.map((pdf) => pdf.id));
    const filteredEvaluations = updatedPdfEvaluations.filter((pe) =>
      validPdfIds.has(pe.pdfId)
    );

    if (
      filteredEvaluations.length !==
      formData.evaluationSheet.pdfEvaluations.length
    ) {
      const newTotalWeight = filteredEvaluations.reduce(
        (sum, pe) => sum + (pe.totalWeight || 0),
        0
      );
      setFormData((prev) => ({
        ...prev,
        evaluationSheet: {
          pdfEvaluations: filteredEvaluations,
          totalWeight: newTotalWeight,
        },
      }));
    }
  }, [formData.pdfs]);

  const validateStep = () => {
    const newErrors: typeof errors = {};

    if (activeStep === 0) {
      if (!formData.name.trim()) {
        newErrors.name = "Class name is required";
      }
      if (!formData.description.trim()) {
        newErrors.description = "Description is required";
      }
    } else if (activeStep === 1) {
      if (formData.pdfs.length === 0) {
        newErrors.pdfs = "At least one PDF is required";
      }
      formData.pdfs.forEach((pdf) => {
        if (!pdf.title.trim()) {
          newErrors.pdfs = "All PDFs must have a title";
        }
        if (!pdf.description.trim()) {
          newErrors.pdfs = "All PDFs must have a description";
        }
      });

      // Check for circular dependencies in prerequisites
      const hasCircularDependency = formData.pdfs.some((pdf) => {
        if (!pdf.prerequisiteId) return false;
        let current = pdf;
        const visited = new Set<string>();

        while (current.prerequisiteId) {
          if (visited.has(current.prerequisiteId)) {
            return true; // Circular dependency found
          }
          visited.add(current.prerequisiteId);
          current = formData.pdfs.find((p) => p.id === current.prerequisiteId)!;
        }
        return false;
      });

      if (hasCircularDependency) {
        newErrors.pdfs = "Circular prerequisites are not allowed";
      }
    } else if (activeStep === 2) {
      if (formData.evaluationSheet.pdfEvaluations.length === 0) {
        newErrors.evaluationSheet = "At least one evaluation is required";
      }
      formData.evaluationSheet.pdfEvaluations.forEach((pdfEval) => {
        if (pdfEval.questions.length === 0) {
          newErrors.evaluationSheet = "All evaluations must have questions";
        }
        pdfEval.questions.forEach((question) => {
          if (!question.text.trim()) {
            newErrors.evaluationSheet = "All question texts are required";
          }
          if (question.weight <= 0) {
            newErrors.evaluationSheet =
              "Question weights must be greater than 0";
          }
        });
      });

      // Validate that weights sum to 100%
      for (const pdfEval of formData.evaluationSheet.pdfEvaluations) {
        const totalWeight = pdfEval.questions.reduce(
          (sum, q) => sum + q.weight,
          0
        );
        if (Math.abs(totalWeight - 100) > 0.1) {
          newErrors.evaluationSheet =
            "Question weights for each PDF must sum to 100%";
          break; // No need to check further if one is invalid
        }
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = async () => {
    if (validateStep()) {
      if (activeStep === steps.length - 1) {
        try {
          setIsSubmitting(true);
          setErrors({}); // Clear any previous errors

          const createClassData: CreateClassRequest = {
            name: formData.name,
            description: formData.description,
            pdfs: formData.pdfs,
            evaluationSheet: formData.evaluationSheet,
          };

          await courseService.createCourse(createClassData);
          navigate("/educator-dashboard");
        } catch (error) {
          console.error("Error creating course:", error);

          // Show the specific error message
          const errorMessage =
            error instanceof Error
              ? error.message
              : "An unexpected error occurred. Please try again.";
          console.error("Error creating course:", errorMessage);
          setErrors((prev) => ({
            ...prev,
            submit: "Failed to create course. Please try again.",
          }));
        } finally {
          setIsSubmitting(false);
        }
      } else {
        setActiveStep((prev) => prev + 1);
      }
    }
  };

  const handleBack = () => {
    setActiveStep((prev) => prev - 1);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    // Clear error when user starts typing
    if (errors[name as keyof typeof errors]) {
      setErrors((prev) => ({
        ...prev,
        [name]: undefined,
      }));
    }
  };

  const handlePDFsChange = (pdfs: PDFContent[]) => {
    setFormData((prev) => ({
      ...prev,
      pdfs,
    }));
    if (errors.pdfs) {
      setErrors((prev) => ({
        ...prev,
        pdfs: undefined,
      }));
    }
  };

  const handleEvaluationSheetChange = (evaluationSheet: EvaluationSheet) => {
    setFormData((prev) => ({
      ...prev,
      evaluationSheet,
    }));
    if (errors.evaluationSheet) {
      setErrors((prev) => ({
        ...prev,
        evaluationSheet: undefined,
      }));
    }
  };

  const renderStepContent = () => {
    switch (activeStep) {
      case 0:
        return (
          <Box sx={{ mt: 2 }}>
            <TextField
              fullWidth
              label="Class Name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              error={!!errors.name}
              helperText={errors.name}
              sx={{ mb: 3 }}
            />
            <TextField
              fullWidth
              label="Description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              error={!!errors.description}
              helperText={errors.description}
              multiline
              rows={4}
              sx={{ mb: 3 }}
            />
          </Box>
        );
      case 1:
        return (
          <Box sx={{ mt: 2 }}>
            <PDFManagement
              pdfs={formData.pdfs}
              onPDFsChange={handlePDFsChange}
              errors={errors}
            />
          </Box>
        );
      case 2:
        return (
          <Box sx={{ mt: 2 }}>
            <EvaluationSheetManagement
              evaluationSheet={formData.evaluationSheet}
              pdfs={formData.pdfs.map((pdf) => ({
                id: pdf.id,
                title: pdf.title,
                file: pdf.file,
                description: pdf.description
              }))}
              onEvaluationSheetChange={handleEvaluationSheetChange}
              errors={errors}
            />
          </Box>
        );
      case 3:
        if (errors.submit) {
          return <ReviewError error={errors.submit} />;
        }
        return (
          <Box sx={{ mt: 2 }}>
            <Typography variant="h6" sx={{ mb: 3 }}>
              Review Your Class
            </Typography>

            <Typography
              variant="subtitle1"
              sx={{ mb: 2, color: theme.palette.primary.main }}
            >
              Basic Information
            </Typography>
            <Typography>
              <strong>Name:</strong> {formData.name}
            </Typography>
            <Typography>
              <strong>Description:</strong> {formData.description}
            </Typography>

            <Typography
              variant="subtitle1"
              sx={{ mt: 4, mb: 2, color: theme.palette.primary.main }}
            >
              Content & Evaluation
            </Typography>
            {formData.pdfs.map((pdf, index) => {
              const pdfEval = formData.evaluationSheet.pdfEvaluations.find(
                (pe) => pe.pdfId === pdf.id
              );
              return (
                <Box key={pdf.id} sx={{ ml: 2, mb: 2 }}>
                  <Typography>
                    <strong>
                      {index + 1}. {pdf.title}
                    </strong>
                  </Typography>
                  <Typography sx={{ ml: 2 }}>
                    Questions: {pdfEval?.questions.length || 0}
                  </Typography>
                  <Typography sx={{ ml: 2 }}>
                    Total Weight: {pdfEval?.totalWeight || 0}%
                  </Typography>
                </Box>
              );
            })}

            <Typography
              variant="subtitle1"
              sx={{ mt: 4, mb: 2, color: theme.palette.primary.main }}
            >
              Total Evaluation
            </Typography>
            <Typography>
              <strong>Total Questions:</strong>{" "}
              {formData.evaluationSheet.pdfEvaluations.reduce(
                (sum, pe) => sum + pe.questions.length,
                0
              )}
            </Typography>
            <Typography>
              <strong>Total Score:</strong>{" "}
              {formData.evaluationSheet.totalWeight}%
            </Typography>
          </Box>
        );
      default:
        return null;
    }
  };

  return (
    <Box>
      <Paper
        sx={{
          p: 3,
          background: "rgba(17, 34, 64, 0.95)",
          backdropFilter: "blur(10px)",
          borderRadius: 2,
          border: `1px solid ${theme.palette.divider}`,
          mb: 3,
        }}
      >
        <Typography variant="h4" sx={{ mb: 4, color: "text.primary" }}>
          Create New Class
        </Typography>

        <Stepper
          activeStep={activeStep}
          sx={{
            mb: 4,
            "& .MuiStepLabel-label": {
              color: "text.secondary",
              "&.Mui-active": {
                color: theme.palette.primary.main,
              },
              "&.Mui-completed": {
                color: theme.palette.success.main,
              },
            },
          }}
        >
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        {renderStepContent()}

        <Box sx={{ display: "flex", justifyContent: "space-between", mt: 4 }}>
          <Button
            variant="outlined"
            onClick={() => navigate("/educator-dashboard")}
            sx={{
              borderColor: theme.palette.primary.main,
              color: theme.palette.primary.main,
              "&:hover": {
                borderColor: theme.palette.primary.dark,
                backgroundColor: "rgba(100, 255, 218, 0.1)",
              },
            }}
          >
            Cancel
          </Button>
          <Box sx={{ display: "flex", gap: 2 }}>
            <Button
              disabled={activeStep === 0}
              onClick={handleBack}
              sx={{
                color: "text.secondary",
                "&:hover": {
                  backgroundColor: "rgba(255, 255, 255, 0.05)",
                },
              }}
            >
              Back
            </Button>
            <Button
              variant="contained"
              onClick={handleNext}
              disabled={isSubmitting}
              sx={{
                background: "linear-gradient(45deg, #64FFDA, #7B89F4)",
                color: "#0A192F",
                "&:hover": {
                  background: "linear-gradient(45deg, #5A6AD4, #A5B4FF)",
                },
                minWidth: activeStep === steps.length - 1 ? "150px" : "auto",
              }}
            >
              {activeStep === steps.length - 1 ? (
                isSubmitting ? (
                  <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                    <CircularProgress size={20} color="inherit" />
                    Creating...
                  </Box>
                ) : (
                  "Create Class"
                )
              ) : (
                "Next"
              )}
            </Button>
          </Box>
        </Box>
      </Paper>
    </Box>
  );
};

export default CreateClass;
