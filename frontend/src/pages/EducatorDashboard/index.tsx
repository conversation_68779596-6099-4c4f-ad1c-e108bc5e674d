import React, { useState, useEffect } from "react";
import {
  Box,
  Grid2 as Grid,
  Paper,
  Typography,
  <PERSON><PERSON>,
  Card,
  CardContent,
  IconButton,
  Chip,
  useTheme,
  LinearProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Snackbar,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import EditIcon from "@mui/icons-material/Edit";
import PeopleIcon from "@mui/icons-material/People";
import AssignmentIcon from "@mui/icons-material/Assignment";
import CalendarTodayIcon from "@mui/icons-material/CalendarToday";
import ShareIcon from "@mui/icons-material/Share";
import PendingActionsIcon from "@mui/icons-material/PendingActions";
import { useNavigate } from "react-router-dom";
import api from "@/services/api";
import RegistrationManagement from "@/components/RegistrationManagement";
import RegistrationNotificationBadge from "@/components/RegistrationNotificationBadge";

// Classes are courses in the API, so we use the term "class" for consistency in the UI.
interface Class {
  id: string;
  name: string;
  studentsCount: number;
  description: string;
  status: "active" | "draft";
  lastActive: string;
  evaluationsCompleted: number;
  evaluationsPending: number;
  progressPercent: number;
  pendingRegistrationsCount: number;
}

interface ShareDialogState {
  open: boolean;
  classId: string | null;
  className: string;
  link: string;
}

const EducatorDashboard: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [classes, setClasses] = useState<Class[]>([]);
  const [shareDialog, setShareDialog] = useState<ShareDialogState>({
    open: false,
    classId: null,
    className: "",
    link: "",
  });
  const [customMessage, setCustomMessage] = useState("");
  const [snackbarOpen, setSnackbarOpen] = useState(false);

  const fetchClasses = async () => {
    try {
      setLoading(true);
      const response = await api.get("/projects/courses/");

      // Transform the API response to match our interface
      const transformedClasses = response.data.map((course: any) => ({
        id: course.code, // Using code as ID since it's the primary key
        name: course.name,
        description: course.description,
        studentsCount: course.student_count || 0,
        status: course.status || "draft", // Default to draft if no status is provided
        lastActive:
          course.last_active || new Date().toISOString().split("T")[0],
        evaluationsCompleted: course.evaluations_completed || 0,
        evaluationsPending: course.evaluations_pending || 0,
        progressPercent: course.progress_percent || 0,
        pendingRegistrationsCount: course.pending_registrations_count || 0,
      }));

      setClasses(transformedClasses);
      setError(null);
    } catch (err: any) {
      console.error("Error fetching classes:", err);
      setError(
        err.response?.data?.detail ||
          "Error fetching classes. Please try again later."
      );
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchClasses();
  }, []);

  const getStatusColor = (status: string) => {
    return status === "active"
      ? theme.palette.success.main
      : theme.palette.warning.main;
  };

  const handleShare = (classId: string) => {
    const classItem = classes.find((c) => c.id === classId);
    if (classItem) {
      // Generate a shareable link - replace with your actual domain
      const shareableLink = `${window.location.origin}/join-class/${classId}`;
      setShareDialog({
        open: true,
        classId,
        className: classItem.name,
        link: shareableLink,
      });
      setCustomMessage(
        `Join my class "${classItem.name}" on DevSpace! Click here to join: ${shareableLink}`
      );
    }
  };

  const handleCloseShare = () => {
    setShareDialog({
      open: false,
      classId: null,
      className: "",
      link: "",
    });
    setCustomMessage("");
  };

  const handleCopyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(customMessage);
      setSnackbarOpen(true);
    } catch (err) {
      console.error("Failed to copy text: ", err);
    }
  };

  if (loading) {
    return (
      <Box sx={{ textAlign: "center", mt: 5 }}>
        <Typography variant="h6" sx={{ color: "text.primary" }}>
          Loading classes...
        </Typography>
      </Box>
    );
  }
  if (error) {
    return (
      <Box sx={{ textAlign: "center", mt: 5 }}>
        <Typography variant="h6" sx={{ color: "error.main" }}>
          {error}
        </Typography>
      </Box>
    );
  }

  return (
    <Box>
      {/* Header Section */}
      <Box
        sx={{
          mb: 4,
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          flexDirection: { xs: "column", sm: "row" },
          gap: { xs: 2, sm: 0 },
        }}
      >
        <Typography
          variant="h4"
          sx={{ color: "text.primary", fontWeight: 600 }}
        >
          Educator Dashboard
        </Typography>
        <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => navigate("/create-class")}
            sx={{
              background: "linear-gradient(45deg, #64FFDA, #7B89F4)",
              color: "#0A192F",
              "&:hover": {
                background: "linear-gradient(45deg, #5A6AD4, #A5B4FF)",
              },
              width: { xs: "100%", sm: "auto" },
            }}
          >
            New Course
          </Button>
          <RegistrationNotificationBadge />
        </Box>
      </Box>

      {/* Stats Overview */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid xs={12} sm={6}>
          <Paper
            sx={{
              p: 3,
              background: "rgba(17, 34, 64, 0.95)",
              backdropFilter: "blur(10px)",
              borderRadius: 2,
              border: `1px solid ${theme.palette.divider}`,
            }}
          >
            <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
              <AssignmentIcon
                sx={{ color: theme.palette.primary.main, mr: 1 }}
              />
              <Typography variant="h6" sx={{ color: "text.primary" }}>
                Active Classes
              </Typography>
            </Box>
            <Typography variant="h3" sx={{ color: theme.palette.primary.main }}>
              {classes.filter((c) => c.status === "active").length}
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} sm={6}>
          <Paper
            sx={{
              p: 3,
              background: "rgba(17, 34, 64, 0.95)",
              backdropFilter: "blur(10px)",
              borderRadius: 2,
              border: `1px solid ${theme.palette.divider}`,
            }}
          >
            <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
              <PeopleIcon sx={{ color: theme.palette.secondary.main, mr: 1 }} />
              <Typography variant="h6" sx={{ color: "text.primary" }}>
                Total Students
              </Typography>
            </Box>
            <Typography
              variant="h3"
              sx={{ color: theme.palette.secondary.main }}
            >
              {classes.reduce((acc, curr) => acc + curr.studentsCount, 0)}
            </Typography>
          </Paper>
        </Grid>
      </Grid>

      {/* Registration Management Section */}
      <Box sx={{ mb: 4 }}>
        <RegistrationManagement />
      </Box>

      {/* Classes Grid */}
      <Typography variant="h5" sx={{ mb: 3, color: "text.primary" }}>
        Your Classes
      </Typography>
      <Grid container spacing={3}>
        {classes.map((classItem) => (
          <Grid item xs={12} md={6} key={classItem.id}>
            <Card
              sx={{
                background: "rgba(17, 34, 64, 0.95)",
                backdropFilter: "blur(10px)",
                borderRadius: 2,
                border: `1px solid ${theme.palette.divider}`,
                transition: "transform 0.2s",
                "&:hover": {
                  transform: "translateY(-4px)",
                },
              }}
            >
              <CardContent>
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "flex-start",
                    mb: 2,
                  }}
                >
                  <Typography
                    variant="h6"
                    sx={{ color: "text.primary", fontWeight: 500 }}
                  >
                    {classItem.name}
                  </Typography>
                  <Box sx={{ display: "flex", gap: 1 }}>
                    <IconButton
                      size="small"
                      onClick={() => handleShare(classItem.id)}
                      sx={{
                        color: theme.palette.secondary.main,
                        "&:hover": {
                          backgroundColor: `${theme.palette.secondary.main}20`,
                        },
                      }}
                    >
                      <ShareIcon />
                    </IconButton>
                    <IconButton
                      size="small"
                      onClick={() => navigate(`/edit-class/${classItem.id}`)}
                      sx={{
                        color: theme.palette.primary.main,
                        "&:hover": {
                          backgroundColor: `${theme.palette.primary.main}20`,
                        },
                      }}
                    >
                      <EditIcon />
                    </IconButton>
                  </Box>
                </Box>

                <Typography
                  variant="body2"
                  sx={{ color: "text.secondary", mb: 2 }}
                >
                  {classItem.description}
                </Typography>

                <Box sx={{ mb: 2 }}>
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                      mb: 1,
                    }}
                  >
                    <Typography
                      variant="body2"
                      sx={{ color: "text.secondary" }}
                    >
                      Overall Progress
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{ color: theme.palette.primary.main }}
                    >
                      {classItem.progressPercent}%
                    </Typography>
                  </Box>
                  <LinearProgress
                    variant="determinate"
                    value={classItem.progressPercent}
                    sx={{
                      height: 6,
                      borderRadius: 3,
                      backgroundColor: "rgba(100, 255, 218, 0.1)",
                      "& .MuiLinearProgress-bar": {
                        backgroundColor: theme.palette.primary.main,
                      },
                    }}
                  />
                </Box>

                <Box sx={{ display: "flex", gap: 1, mb: 2, flexWrap: "wrap" }}>
                  <Chip
                    size="small"
                    label={`${classItem.studentsCount} Students`}
                    sx={{
                      backgroundColor: "rgba(123, 137, 244, 0.1)",
                      color: theme.palette.secondary.main,
                    }}
                  />
                  {classItem.evaluationsPending > 0 && (
                    <Chip
                      size="small"
                      label={`${classItem.evaluationsPending} Pending`}
                      sx={{
                        backgroundColor: "rgba(255, 184, 108, 0.1)",
                        color: theme.palette.warning.main,
                      }}
                    />
                  )}
                  {classItem.pendingRegistrationsCount > 0 && (
                    <Chip
                      size="small"
                      label={`${classItem.pendingRegistrationsCount} Registration${classItem.pendingRegistrationsCount !== 1 ? 's' : ''}`}
                      icon={<PendingActionsIcon sx={{ fontSize: 16 }} />}
                      onClick={() => navigate(`/course/${classItem.id}/registrations`)}
                      sx={{
                        backgroundColor: "rgba(255, 152, 0, 0.1)",
                        color: theme.palette.warning.main,
                        cursor: "pointer",
                        "&:hover": {
                          backgroundColor: "rgba(255, 152, 0, 0.2)",
                        },
                      }}
                    />
                  )}
                </Box>

                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                  }}
                >
                  <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                    <CalendarTodayIcon
                      sx={{ fontSize: 16, color: "text.secondary" }}
                    />
                    <Typography
                      variant="body2"
                      sx={{ color: "text.secondary" }}
                    >
                      Last active: {classItem.lastActive}
                    </Typography>
                  </Box>
                  <Chip
                    label={
                      classItem.status.charAt(0).toUpperCase() +
                      classItem.status.slice(1)
                    }
                    size="small"
                    sx={{
                      backgroundColor: `${getStatusColor(classItem.status)}20`,
                      color: getStatusColor(classItem.status),
                    }}
                  />
                </Box>

                {/* Action Buttons */}
                <Box sx={{ mt: 2, pt: 2, borderTop: `1px solid ${theme.palette.divider}` }}>
                  <Box sx={{ display: "flex", gap: 1 }}>
                    <Button
                      size="small"
                      variant="outlined"
                      startIcon={<PendingActionsIcon />}
                      onClick={() => navigate(`/course/${classItem.id}/registrations`)}
                      sx={{
                        flex: 1,
                        borderColor: theme.palette.warning.main,
                        color: theme.palette.warning.main,
                        "&:hover": {
                          borderColor: theme.palette.warning.dark,
                          backgroundColor: "rgba(255, 152, 0, 0.1)",
                        },
                      }}
                    >
                      Registrations {classItem.pendingRegistrationsCount > 0 && `(${classItem.pendingRegistrationsCount})`}
                    </Button>
                    <IconButton
                      size="small"
                      onClick={() => handleShare(classItem.id)}
                      sx={{ color: "text.secondary" }}
                    >
                      <ShareIcon />
                    </IconButton>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Share Dialog */}
      <Dialog
        open={shareDialog.open}
        onClose={handleCloseShare}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            background: "rgba(17, 34, 64, 0.95)",
            backdropFilter: "blur(10px)",
            borderRadius: 2,
            border: `1px solid ${theme.palette.divider}`,
          },
        }}
      >
        <DialogTitle sx={{ color: "text.primary" }}>
          Share {shareDialog.className}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mb: 3 }}>
            <Typography
              variant="subtitle2"
              sx={{ mb: 1, color: "text.secondary" }}
            >
              Shareable Link
            </Typography>
            <TextField
              fullWidth
              value={shareDialog.link}
              InputProps={{
                readOnly: true,
                sx: { bgcolor: "rgba(255, 255, 255, 0.05)" },
              }}
              sx={{ mb: 2 }}
            />
            <Typography
              variant="subtitle2"
              sx={{ mb: 1, color: "text.secondary" }}
            >
              Customize Message
            </Typography>
            <TextField
              fullWidth
              multiline
              rows={4}
              value={customMessage}
              onChange={(e) => setCustomMessage(e.target.value)}
              placeholder="Add a personal message..."
              sx={{
                "& .MuiInputBase-root": {
                  bgcolor: "rgba(255, 255, 255, 0.05)",
                },
              }}
            />
          </Box>
        </DialogContent>
        <DialogActions sx={{ px: 3, pb: 3 }}>
          <Button onClick={handleCloseShare} sx={{ color: "text.secondary" }}>
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={handleCopyToClipboard}
            sx={{
              background: "linear-gradient(45deg, #64FFDA, #7B89F4)",
              color: "#0A192F",
              "&:hover": {
                background: "linear-gradient(45deg, #5A6AD4, #A5B4FF)",
              },
            }}
          >
            Copy to Clipboard
          </Button>
        </DialogActions>
      </Dialog>

      {/* Copy Success Snackbar */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={3000}
        onClose={() => setSnackbarOpen(false)}
        message="Message copied to clipboard!"
        anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
      />
    </Box>
  );
};

export default EducatorDashboard;
